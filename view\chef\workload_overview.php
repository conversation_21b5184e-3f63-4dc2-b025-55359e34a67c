<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Get the department head's ID and department ID from the session
$chefId = $_SESSION['user']['teacher_id'] ?? null;
$departmentId = $_SESSION['user']['department_id'] ?? null;

// Get department head's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$fullName = trim($prenom . ' ' . $nom);
if (empty($fullName)) {
    $fullName = $_SESSION['user']['username'] ?? 'Chef de département';
}

// Get department name from session
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';

// Page configuration
$pageTitle = "Aperçu de la Charge de Travail";
$currentPage = "workload_overview";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Main Sidebar CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <style>
        /* Override main content styles for proper sidebar integration */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background-color: #f8f9fa;
            width: calc(100% - 280px);
            transition: margin-left 0.3s ease;
        }

        .page-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.75rem;
        }

        /* Responsive styles for mobile */
        @media (max-width: 991.98px) {
            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        /* Ensure sidebar positioning is correct */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1030;
        }

        .workload-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .workload-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }

        .teacher-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .teacher-item:hover {
            background-color: #f8f9fa;
        }

        .teacher-item:last-child {
            border-bottom: none;
        }

        .progress-custom {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }

        .progress-bar-custom {
            border-radius: 4px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .year-selector {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .year-selector:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        /* Styles for teachers below minimum threshold */
        .teacher-item.below-threshold {
            background-color: #fff5f5;
            border-left: 4px solid #dc3545;
            position: relative;
        }

        .teacher-item.below-threshold:hover {
            background-color: #ffe6e6;
        }

        .teacher-item.approaching-threshold {
            background-color: #fff8e1;
            border-left: 4px solid #ff9800;
        }

        .teacher-item.approaching-threshold:hover {
            background-color: #ffecb3;
        }

        .threshold-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            font-size: 1.2rem;
        }

        .threshold-indicator.critical {
            color: #dc3545;
        }

        .threshold-indicator.warning {
            color: #ff9800;
        }

        .threshold-info {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .threshold-info.critical {
            color: #dc3545;
            font-weight: 600;
        }

        .threshold-info.warning {
            color: #ff9800;
            font-weight: 600;
        }

        .progress-bar-critical {
            background-color: #dc3545 !important;
        }

        .progress-bar-warning {
            background-color: #ff9800 !important;
        }
    </style>
</head>
<body>
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="d-flex">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                    <!-- Page Header -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <h1 class="page-title">
                                <i class="fas fa-chart-line me-3"></i>
                                Aperçu de la Charge de Travail
                            </h1>
                            <p class="text-muted mb-0">
                                Suivi dynamique de la charge de travail des enseignants du département <strong><?php echo htmlspecialchars($departmentName); ?></strong>
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <select id="academicYearSelector" class="form-select year-selector">
                                <option value="">Chargement...</option>
                            </select>
                        </div>
                    </div>

                    <!-- Workload Thresholds Info -->
                    <div class="row mb-3" id="thresholdInfo" style="display: none;">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Seuils de charge minimale configurés :</strong>
                                <span id="thresholdDetails">Chargement...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Statistics -->
                    <div class="row mb-4" id="summaryStats">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-value" id="totalTeachers">-</div>
                                <div class="stat-label">Enseignants</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-value" id="totalHours">-</div>
                                <div class="stat-label">Heures Totales</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-value" id="totalAssignments">-</div>
                                <div class="stat-label">Affectations</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-value" id="averageHours">-</div>
                                <div class="stat-label">Moyenne/Enseignant</div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="row">
                        <!-- Teachers Workload List -->
                        <div class="col-lg-8">
                            <div class="workload-card">
                                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-users me-2"></i>
                                        Charge de Travail par Enseignant
                                    </h5>
                                    <button id="sendMessageBtn" class="btn btn-warning btn-sm" style="display: none;" onclick="openMessageModal()">
                                        <i class="fas fa-envelope me-1"></i>
                                        Envoyer un message aux enseignants sous le seuil
                                    </button>
                                </div>
                                <div class="card-body p-0">
                                    <div id="teachersWorkloadList">
                                        <div class="loading-spinner">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Workload Distribution Chart -->
                        <div class="col-lg-4">
                            <div class="workload-card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        Répartition par Type
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="workloadChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- Message Modal -->
    <div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalLabel">
                        <i class="fas fa-envelope me-2"></i>
                        Envoyer un message aux enseignants sous le seuil
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="messageForm">
                        <!-- Recipients Section -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-users me-1"></i>
                                Destinataires
                            </label>
                            <div class="alert alert-info">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    Enseignants dont la charge de travail est sous le seuil minimal configuré. Vous pouvez désélectionner individuellement.
                                </small>
                            </div>
                            <div id="recipientsList" class="border rounded p-3 bg-light">
                                <!-- Recipients will be populated here -->
                            </div>
                        </div>

                        <!-- Subject Field -->
                        <div class="mb-3">
                            <label for="messageSubject" class="form-label fw-bold">
                                <i class="fas fa-tag me-1"></i>
                                Objet du message
                            </label>
                            <input type="text" class="form-control" id="messageSubject"
                                   placeholder="Ex: Rappel concernant votre charge de travail" required>
                        </div>

                        <!-- Message Content -->
                        <div class="mb-3">
                            <label for="messageContent" class="form-label fw-bold">
                                <i class="fas fa-edit me-1"></i>
                                Contenu du message
                            </label>
                            <textarea class="form-control" id="messageContent" rows="6"
                                      placeholder="Rédigez votre message ici..." required></textarea>
                            <div class="form-text">
                                <small>
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Conseil : Soyez constructif et proposez des solutions pour aider les enseignants à atteindre leur charge minimale.
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" onclick="sendGroupMessage()">
                        <i class="fas fa-paper-plane me-1"></i>
                        Envoyer le message
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>

    <script>
        // Global variables
        let currentAcademicYear = '';
        let workloadChart = null;
        let workloadConfigurations = {};
        let teachersBelowThreshold = [];
        let allTeachersData = [];
        const departmentId = <?php echo $departmentId ? $departmentId : 'null'; ?>;
        const chefId = <?php echo $chefId ? $chefId : 'null'; ?>;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if department ID is available
            if (departmentId === null) {
                showErrorMessage();
                return;
            }

            loadAcademicYears();

            // Academic year selector change event
            document.getElementById('academicYearSelector').addEventListener('change', function() {
                currentAcademicYear = this.value;
                if (currentAcademicYear) {
                    loadWorkloadData();
                }
            });
        });

        // Show error message for missing department
        function showErrorMessage() {
            document.getElementById('summaryStats').innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Département non trouvé. Veuillez vous reconnecter.
                    </div>
                </div>
            `;
            document.getElementById('teachersWorkloadList').innerHTML = `
                <div class="no-data">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <p>Impossible de charger les données du département</p>
                </div>
            `;
        }

        // Load available academic years
        async function loadAcademicYears() {
            try {
                const response = await fetch('../../controller/workloadController.php?action=getAvailableAcademicYears');
                const data = await response.json();

                if (data.success) {
                    const selector = document.getElementById('academicYearSelector');
                    selector.innerHTML = '';

                    data.data.forEach((year, index) => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        if (index === 0) {
                            option.selected = true;
                            currentAcademicYear = year;
                        }
                        selector.appendChild(option);
                    });

                    // Load data for the first year
                    if (currentAcademicYear) {
                        loadWorkloadData();
                    }
                } else {
                    console.error('Error loading academic years:', data.error);
                }
            } catch (error) {
                console.error('Error loading academic years:', error);
            }
        }

        // Load workload data for selected academic year
        async function loadWorkloadData() {
            try {
                // Load workload configurations first
                await loadWorkloadConfigurations();

                // Load summary statistics
                await loadSummaryStats();

                // Load teachers workload
                await loadTeachersWorkload();

            } catch (error) {
                console.error('Error loading workload data:', error);
            }
        }

        // Load workload configurations for the current academic year
        async function loadWorkloadConfigurations() {
            try {
                // Reset configurations
                workloadConfigurations = {};

                // Load configurations for enseignant role
                const enseignantResponse = await fetch(`../../controller/configurationChargeController.php?action=getMinimumWorkloadForTeacher&role=enseignant&academic_year=${currentAcademicYear}`);
                if (enseignantResponse.ok) {
                    const enseignantData = await enseignantResponse.json();
                    if (!enseignantData.error) {
                        workloadConfigurations['enseignant'] = parseInt(enseignantData.data.charge_minimale) || 0;
                    }
                }

                // Load configurations for vacataire role
                const vacataireResponse = await fetch(`../../controller/configurationChargeController.php?action=getMinimumWorkloadForTeacher&role=vacataire&academic_year=${currentAcademicYear}`);
                if (vacataireResponse.ok) {
                    const vacataireData = await vacataireResponse.json();
                    if (!vacataireData.error) {
                        workloadConfigurations['vacataire'] = parseInt(vacataireData.data.charge_minimale) || 0;
                    }
                }

                console.log('Loaded workload configurations:', workloadConfigurations);

                // Update threshold info display
                updateThresholdInfoDisplay();

                // Show info message if no configurations are found
                if (Object.keys(workloadConfigurations).length === 0) {
                    console.warn('No workload configurations found for academic year:', currentAcademicYear);
                }
            } catch (error) {
                console.error('Error loading workload configurations:', error);
                // Set default configurations if loading fails
                workloadConfigurations = {};
            }
        }

        // Load summary statistics
        async function loadSummaryStats() {
            try {
                const response = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadSummary&department_id=${departmentId}&academic_year=${currentAcademicYear}`);
                const data = await response.json();

                if (data.success) {
                    const summary = data.data;
                    document.getElementById('totalTeachers').textContent = summary.total_teachers;
                    document.getElementById('totalHours').textContent = summary.total_hours + 'h';
                    document.getElementById('totalAssignments').textContent = summary.total_assignments;
                    document.getElementById('averageHours').textContent = summary.average_hours + 'h';
                } else {
                    console.error('Error loading summary stats:', data.error);
                }
            } catch (error) {
                console.error('Error loading summary stats:', error);
            }
        }

        // Load teachers workload list
        async function loadTeachersWorkload() {
            try {
                const response = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadByYear&department_id=${departmentId}&academic_year=${currentAcademicYear}`);
                const data = await response.json();

                if (data.success) {
                    displayTeachersWorkload(data.data);
                    updateWorkloadChart(data.data);
                } else {
                    console.error('Error loading teachers workload:', data.error);
                    document.getElementById('teachersWorkloadList').innerHTML = '<div class="no-data"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>Erreur lors du chargement des données</p></div>';
                }
            } catch (error) {
                console.error('Error loading teachers workload:', error);
                document.getElementById('teachersWorkloadList').innerHTML = '<div class="no-data"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>Erreur lors du chargement des données</p></div>';
            }
        }

        // Display teachers workload list
        function displayTeachersWorkload(teachers) {
            const container = document.getElementById('teachersWorkloadList');

            if (teachers.length === 0) {
                container.innerHTML = '<div class="no-data"><i class="fas fa-users fa-3x mb-3"></i><p>Aucune donnée de charge trouvée pour cette année</p></div>';
                return;
            }

            // Store all teachers data for messaging
            allTeachersData = teachers;

            // Sort teachers: those below threshold first, then by total hours descending
            const sortedTeachers = [...teachers].sort((a, b) => {
                const aMinimum = workloadConfigurations[a.role] || 0;
                const bMinimum = workloadConfigurations[b.role] || 0;

                const aBelowThreshold = a.total_hours < aMinimum;
                const bBelowThreshold = b.total_hours < bMinimum;

                // If one is below threshold and the other isn't, prioritize the one below
                if (aBelowThreshold && !bBelowThreshold) return -1;
                if (!aBelowThreshold && bBelowThreshold) return 1;

                // If both are below or both are above, sort by total hours descending
                return b.total_hours - a.total_hours;
            });

            const maxHours = Math.max(...teachers.map(t => t.total_hours));

            let html = '';
            sortedTeachers.forEach(teacher => {
                const progressPercentage = maxHours > 0 ? (teacher.total_hours / maxHours) * 100 : 0;
                const minimumHours = workloadConfigurations[teacher.role] || 0;

                // Determine threshold status
                const thresholdStatus = getThresholdStatus(teacher.total_hours, minimumHours);
                const progressColor = getProgressColorWithThreshold(teacher.total_hours, minimumHours);

                html += `
                    <div class="teacher-item ${thresholdStatus.cssClass}">
                        ${thresholdStatus.indicator ? `<div class="threshold-indicator ${thresholdStatus.indicatorClass}"><i class="${thresholdStatus.icon}"></i></div>` : ''}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-user me-2"></i>
                                    ${teacher.prenom} ${teacher.nom}
                                </h6>
                                <small class="text-muted">${teacher.role}</small>
                                ${thresholdStatus.info ? `<div class="threshold-info ${thresholdStatus.infoClass}">${thresholdStatus.info}</div>` : ''}
                            </div>
                            <div class="text-end">
                                <strong class="text-primary">${teacher.total_hours}h</strong>
                                <br>
                                <small class="text-muted">${teacher.total_assignments} affectations</small>
                            </div>
                        </div>
                        <div class="progress progress-custom mb-2">
                            <div class="progress-bar progress-bar-custom ${progressColor}"
                                 style="width: ${progressPercentage}%"></div>
                        </div>
                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted">Cours: ${teacher.cours_hours}h</small>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">TD: ${teacher.td_hours}h</small>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">TP: ${teacher.tp_hours}h</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;

            // Identify teachers below threshold and update button visibility
            identifyTeachersBelowThreshold(sortedTeachers);
        }

        // Get threshold status for a teacher
        function getThresholdStatus(totalHours, minimumHours) {
            if (minimumHours === 0) {
                return {
                    cssClass: '',
                    indicator: false,
                    info: null
                };
            }

            const percentage = (totalHours / minimumHours) * 100;

            if (totalHours < minimumHours) {
                const remaining = minimumHours - totalHours;
                return {
                    cssClass: 'below-threshold',
                    indicator: true,
                    indicatorClass: 'critical',
                    icon: 'fas fa-exclamation-triangle',
                    info: `Manque ${remaining}h (${Math.round(percentage)}% du minimum)`,
                    infoClass: 'critical'
                };
            } else if (percentage < 120) { // Within 20% above minimum
                return {
                    cssClass: 'approaching-threshold',
                    indicator: true,
                    indicatorClass: 'warning',
                    icon: 'fas fa-exclamation-circle',
                    info: `${Math.round(percentage)}% du minimum atteint`,
                    infoClass: 'warning'
                };
            }

            return {
                cssClass: '',
                indicator: false,
                info: null
            };
        }

        // Get progress bar color with threshold consideration
        function getProgressColorWithThreshold(totalHours, minimumHours) {
            if (minimumHours > 0) {
                if (totalHours < minimumHours) {
                    return 'progress-bar-critical';
                } else if ((totalHours / minimumHours) * 100 < 120) {
                    return 'progress-bar-warning';
                }
            }

            // Default color logic for teachers above threshold or without configuration
            if (totalHours >= 200) return 'bg-success';
            if (totalHours >= 150) return 'bg-info';
            if (totalHours >= 100) return 'bg-primary';
            return 'bg-secondary';
        }

        // Get progress bar color based on hours (legacy function for compatibility)
        function getProgressColor(hours) {
            if (hours >= 200) return 'success';
            if (hours >= 150) return 'warning';
            if (hours >= 100) return 'info';
            return 'secondary';
        }

        // Update threshold info display
        function updateThresholdInfoDisplay() {
            const thresholdInfo = document.getElementById('thresholdInfo');
            const thresholdDetails = document.getElementById('thresholdDetails');

            if (Object.keys(workloadConfigurations).length === 0) {
                thresholdInfo.style.display = 'none';
                return;
            }

            let details = [];
            if (workloadConfigurations['enseignant']) {
                details.push(`Enseignants permanents: ${workloadConfigurations['enseignant']}h`);
            }
            if (workloadConfigurations['vacataire']) {
                details.push(`Enseignants vacataires: ${workloadConfigurations['vacataire']}h`);
            }

            if (details.length > 0) {
                thresholdDetails.textContent = details.join(' | ');
                thresholdInfo.style.display = 'block';
            } else {
                thresholdInfo.style.display = 'none';
            }
        }

        // Identify teachers below threshold
        function identifyTeachersBelowThreshold(teachers) {
            teachersBelowThreshold = [];

            teachers.forEach(teacher => {
                const minimumHours = workloadConfigurations[teacher.role] || 0;
                if (minimumHours > 0 && teacher.total_hours < minimumHours) {
                    teachersBelowThreshold.push({
                        id: teacher.id_enseignant,
                        nom: teacher.nom,
                        prenom: teacher.prenom,
                        role: teacher.role,
                        total_hours: teacher.total_hours,
                        minimum_hours: minimumHours,
                        deficit: minimumHours - teacher.total_hours
                    });
                }
            });

            // Show/hide the send message button
            const sendMessageBtn = document.getElementById('sendMessageBtn');
            if (teachersBelowThreshold.length > 0) {
                sendMessageBtn.style.display = 'block';
                sendMessageBtn.innerHTML = `
                    <i class="fas fa-envelope me-1"></i>
                    Envoyer un message (${teachersBelowThreshold.length} enseignant${teachersBelowThreshold.length > 1 ? 's' : ''})
                `;
            } else {
                sendMessageBtn.style.display = 'none';
            }
        }

        // Open message modal
        function openMessageModal() {
            if (teachersBelowThreshold.length === 0) {
                showNotification('Aucun enseignant sous le seuil minimal trouvé.', 'warning');
                return;
            }

            // Populate recipients list
            populateRecipientsList();

            // Set default subject
            document.getElementById('messageSubject').value = `Rappel concernant votre charge de travail - ${currentAcademicYear}`;

            // Set default message content
            const defaultMessage = `Bonjour,

Suite à notre suivi de la charge de travail pour l'année académique ${currentAcademicYear}, nous avons constaté que votre charge actuelle est en dessous du seuil minimal configuré pour votre rôle.

Nous vous invitons à prendre contact avec le département pour discuter des possibilités d'augmentation de votre charge de travail.

Cordialement,
Le département`;

            document.getElementById('messageContent').value = defaultMessage;

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('messageModal'));
            modal.show();
        }

        // Populate recipients list in modal
        function populateRecipientsList() {
            const container = document.getElementById('recipientsList');

            if (teachersBelowThreshold.length === 0) {
                container.innerHTML = '<p class="text-muted">Aucun enseignant sous le seuil minimal.</p>';
                return;
            }

            let html = '';
            teachersBelowThreshold.forEach((teacher, index) => {
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" value="${teacher.id}"
                               id="recipient_${teacher.id}" checked>
                        <label class="form-check-label" for="recipient_${teacher.id}">
                            <strong>${teacher.prenom} ${teacher.nom}</strong> (${teacher.role})
                            <br>
                            <small class="text-muted">
                                Charge actuelle: ${teacher.total_hours}h |
                                Minimum requis: ${teacher.minimum_hours}h |
                                Déficit: ${teacher.deficit}h
                            </small>
                        </label>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Send group message
        async function sendGroupMessage() {
            const subject = document.getElementById('messageSubject').value.trim();
            const content = document.getElementById('messageContent').value.trim();

            // Validation
            if (!subject) {
                showNotification('Veuillez saisir un objet pour le message.', 'error');
                return;
            }

            if (!content) {
                showNotification('Veuillez saisir le contenu du message.', 'error');
                return;
            }

            // Get selected recipients
            const selectedRecipients = [];
            const checkboxes = document.querySelectorAll('#recipientsList input[type="checkbox"]:checked');

            checkboxes.forEach(checkbox => {
                selectedRecipients.push(parseInt(checkbox.value));
            });

            if (selectedRecipients.length === 0) {
                showNotification('Veuillez sélectionner au moins un destinataire.', 'error');
                return;
            }

            // Show loading state
            const sendButton = document.querySelector('#messageModal .btn-primary');
            const originalText = sendButton.innerHTML;
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Envoi en cours...';
            sendButton.disabled = true;

            try {
                // Send messages
                const response = await fetch('../../controller/workloadMessageController.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'sendGroupMessage',
                        sender_id: chefId,
                        recipients: selectedRecipients,
                        subject: subject,
                        content: content,
                        academic_year: currentAcademicYear
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`Message envoyé avec succès à ${result.sent_count} enseignant(s).`, 'success');

                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('messageModal'));
                    modal.hide();

                    // Reset form
                    document.getElementById('messageForm').reset();
                } else {
                    showNotification('Erreur lors de l\'envoi du message: ' + (result.error || 'Erreur inconnue'), 'error');
                }
            } catch (error) {
                console.error('Error sending message:', error);
                showNotification('Erreur lors de l\'envoi du message. Veuillez réessayer.', 'error');
            } finally {
                // Restore button state
                sendButton.innerHTML = originalText;
                sendButton.disabled = false;
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification-toast');
            existingNotifications.forEach(notification => notification.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification-toast alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                max-width: 500px;
            `;

            const icon = type === 'success' ? 'check-circle' :
                        type === 'error' ? 'exclamation-triangle' :
                        type === 'warning' ? 'exclamation-circle' : 'info-circle';

            notification.innerHTML = `
                <i class="fas fa-${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Update workload distribution chart
        function updateWorkloadChart(teachers) {
            const ctx = document.getElementById('workloadChart').getContext('2d');

            // Calculate totals by type
            const coursTotal = teachers.reduce((sum, t) => sum + parseInt(t.cours_hours), 0);
            const tdTotal = teachers.reduce((sum, t) => sum + parseInt(t.td_hours), 0);
            const tpTotal = teachers.reduce((sum, t) => sum + parseInt(t.tp_hours), 0);

            if (workloadChart) {
                workloadChart.destroy();
            }

            workloadChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Cours', 'TD', 'TP'],
                    datasets: [{
                        data: [coursTotal, tdTotal, tpTotal],
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + 'h';
                                }
                            }
                        }
                    }
                }
            });
        }


    </script>
</body>
</html>
