<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Get the department head's ID and department ID from the session
$chefId = $_SESSION['user']['teacher_id'] ?? null;
$departmentId = $_SESSION['user']['department_id'] ?? null;

// Get department head's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$fullName = trim($prenom . ' ' . $nom);
if (empty($fullName)) {
    $fullName = $_SESSION['user']['username'] ?? 'Chef de département';
}

// Get department name from session
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';

// Page configuration
$pageTitle = "Rapports du Département";
$currentPage = "reporting.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- jsPDF for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- Main Sidebar CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <style>
        /* Override main content styles for proper sidebar integration */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background-color: #f8f9fa;
            width: calc(100% - 280px);
            transition: margin-left 0.3s ease;
        }

        .page-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.75rem;
        }

        /* Responsive styles for mobile */
        @media (max-width: 991.98px) {
            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        .report-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }

        .filter-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .filter-section .form-select,
        .filter-section .form-control {
            background-color: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 8px;
        }

        .btn-generate {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .btn-export {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            color: white;
        }

        .report-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #495057;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        @media print {
            .no-print {
                display: none !important;
            }
            
            .main-content {
                margin-left: 0;
                width: 100%;
            }
            
            .report-content {
                box-shadow: none;
                border: 1px solid #dee2e6;
            }
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <!-- Page Header -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h1 class="page-title">
                            <i class="fas fa-file-alt me-3"></i>
                            Rapports du Département
                        </h1>
                        <p class="text-muted mb-0">
                            Génération de rapports détaillés pour le département <strong><?php echo htmlspecialchars($departmentName); ?></strong>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <button class="btn btn-export no-print" onclick="exportToPDF()" style="display: none;" id="exportBtn">
                            <i class="fas fa-file-pdf me-2"></i>
                            Exporter en PDF
                        </button>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="filter-section no-print">
                    <h5 class="mb-3">
                        <i class="fas fa-filter me-2"></i>
                        Paramètres du Rapport
                    </h5>
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Année Académique</label>
                            <select id="academicYearFilter" class="form-select">
                                <option value="">Chargement...</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Semestre</label>
                            <select id="semesterFilter" class="form-select">
                                <option value="">Tous les semestres</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Type d'Enseignant</label>
                            <select id="teacherTypeFilter" class="form-select">
                                <option value="">Tous les types</option>
                                <option value="enseignant">Enseignants Permanents</option>
                                <option value="vacataire">Enseignants Vacataires</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Module Spécifique</label>
                            <select id="moduleFilter" class="form-select">
                                <option value="">Tous les modules</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-center">
                            <button class="btn btn-generate" onclick="generateReport()">
                                <i class="fas fa-chart-bar me-2"></i>
                                Générer le Rapport
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Content -->
                <div id="reportContent" style="display: none;">
                    <!-- Report will be generated here -->
                </div>

                <!-- Default Message -->
                <div id="defaultMessage" class="report-content text-center">
                    <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Génération de Rapports</h4>
                    <p class="text-muted">Sélectionnez les paramètres ci-dessus et cliquez sur "Générer le Rapport" pour créer un rapport détaillé du département.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>

    <script>
        // Global variables
        let currentFilters = {};
        let reportData = {};
        let charts = {};
        const departmentId = <?php echo $departmentId ? $departmentId : 'null'; ?>;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            if (departmentId === null) {
                showError('Département non trouvé. Veuillez vous reconnecter.');
                return;
            }

            loadInitialData();
        });

        // Load initial data for filters
        async function loadInitialData() {
            try {
                await Promise.all([
                    loadAcademicYears(),
                    loadSemesters(),
                    loadModules()
                ]);
            } catch (error) {
                console.error('Error loading initial data:', error);
                showError('Erreur lors du chargement des données initiales.');
            }
        }

        // Load academic years
        async function loadAcademicYears() {
            try {
                const response = await fetch('../../controller/workloadController.php?action=getAvailableAcademicYears');
                const data = await response.json();

                if (data.success) {
                    const selector = document.getElementById('academicYearFilter');
                    selector.innerHTML = '<option value="">Sélectionner une année</option>';

                    data.data.forEach((year, index) => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        if (index === 0) option.selected = true;
                        selector.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading academic years:', error);
            }
        }

        // Load semesters
        async function loadSemesters() {
            try {
                const response = await fetch('../../controller/moduleController.php?action=getSemesters');
                const data = await response.json();

                if (data.success) {
                    const selector = document.getElementById('semesterFilter');
                    data.data.forEach(semester => {
                        const option = document.createElement('option');
                        option.value = semester.id;
                        option.textContent = semester.nom;
                        selector.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading semesters:', error);
            }
        }

        // Load modules
        async function loadModules() {
            try {
                const response = await fetch(`../../controller/moduleController.php?action=getDepartmentModules&department_id=${departmentId}`);
                const data = await response.json();

                if (data.success) {
                    const selector = document.getElementById('moduleFilter');
                    data.data.forEach(module => {
                        const option = document.createElement('option');
                        option.value = module.id;
                        option.textContent = module.nom;
                        selector.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading modules:', error);
            }
        }

        // Generate report
        async function generateReport() {
            // Get filter values
            currentFilters = {
                academicYear: document.getElementById('academicYearFilter').value,
                semester: document.getElementById('semesterFilter').value,
                teacherType: document.getElementById('teacherTypeFilter').value,
                module: document.getElementById('moduleFilter').value
            };

            if (!currentFilters.academicYear) {
                showError('Veuillez sélectionner une année académique.');
                return;
            }

            // Show loading
            showLoading();

            try {
                // Load all report data
                await Promise.all([
                    loadDepartmentStats(),
                    loadModulesData(),
                    loadWorkloadData(),
                    loadTeachersData()
                ]);

                // Generate and display report
                displayReport();

                // Show export button
                document.getElementById('exportBtn').style.display = 'inline-block';

            } catch (error) {
                console.error('Error generating report:', error);
                showError('Erreur lors de la génération du rapport.');
            }
        }

        // Load department statistics
        async function loadDepartmentStats() {
            const response = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadSummary&department_id=${departmentId}&academic_year=${currentFilters.academicYear}`);
            const data = await response.json();

            if (data.success) {
                reportData.stats = data.data;
            } else {
                reportData.stats = {};
            }
        }

        // Load modules data
        async function loadModulesData() {
            let url = `../../controller/moduleController.php?action=getDepartmentModulesWithAssignments&department_id=${departmentId}&academic_year=${currentFilters.academicYear}`;

            if (currentFilters.semester) {
                url += `&semester_id=${currentFilters.semester}`;
            }
            if (currentFilters.module) {
                url += `&module_id=${currentFilters.module}`;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                reportData.modules = data.data;
            } else {
                reportData.modules = [];
            }
        }

        // Load workload data
        async function loadWorkloadData() {
            let url = `../../controller/workloadController.php?action=getDepartmentWorkloadByYear&department_id=${departmentId}&academic_year=${currentFilters.academicYear}`;

            if (currentFilters.teacherType) {
                url += `&teacher_type=${currentFilters.teacherType}`;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                reportData.workload = data.data;
            } else {
                reportData.workload = [];
            }
        }

        // Load teachers data
        async function loadTeachersData() {
            let url = `../../controller/teacherController.php?action=getDepartmentTeachers&department_id=${departmentId}`;

            if (currentFilters.teacherType) {
                url += `&teacher_type=${currentFilters.teacherType}`;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                reportData.teachers = data.data;
            } else {
                reportData.teachers = [];
            }
        }

        // Display the generated report
        function displayReport() {
            const reportContent = document.getElementById('reportContent');
            const defaultMessage = document.getElementById('defaultMessage');

            // Hide default message and show report
            defaultMessage.style.display = 'none';
            reportContent.style.display = 'block';

            // Generate report HTML
            const reportHTML = generateReportHTML();
            reportContent.innerHTML = reportHTML;

            // Create charts
            setTimeout(() => {
                createWorkloadChart();
                createModulesChart();
            }, 100);
        }

        // Generate report HTML
        function generateReportHTML() {
            const currentDate = new Date().toLocaleDateString('fr-FR');
            const stats = reportData.stats || {};
            const workload = reportData.workload || [];
            const modules = reportData.modules || [];
            const teachers = reportData.teachers || [];

            return `
                <div class="report-content">
                    <!-- Report Header -->
                    <div class="text-center mb-4">
                        <h2 class="text-primary">Rapport du Département</h2>
                        <h4 class="text-muted"><?php echo htmlspecialchars($departmentName); ?></h4>
                        <p class="text-muted">Année Académique: ${currentFilters.academicYear} | Généré le: ${currentDate}</p>
                    </div>

                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-value">${stats.total_teachers || 0}</div>
                                <div class="stat-label">Enseignants</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-value">${modules.length}</div>
                                <div class="stat-label">Modules</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-value">${stats.total_hours || 0}h</div>
                                <div class="stat-label">Heures Totales</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-value">${Math.round(stats.average_hours || 0)}h</div>
                                <div class="stat-label">Moyenne/Enseignant</div>
                            </div>
                        </div>
                    </div>

                    <!-- Workload Distribution Chart -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-chart-pie me-2"></i>Répartition de la Charge</h5>
                            <div class="chart-container">
                                <canvas id="workloadChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-chart-bar me-2"></i>Modules par Semestre</h5>
                            <div class="chart-container">
                                <canvas id="modulesChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Teachers Summary -->
                    <div class="mb-4">
                        <h5><i class="fas fa-users me-2"></i>Résumé des Enseignants</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Nom</th>
                                        <th>Type</th>
                                        <th>Charge Totale</th>
                                        <th>Cours</th>
                                        <th>TD</th>
                                        <th>TP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${workload.map(teacher => `
                                        <tr>
                                            <td>${teacher.prenom} ${teacher.nom}</td>
                                            <td><span class="badge ${teacher.role === 'enseignant' ? 'bg-success' : 'bg-info'}">${teacher.role === 'enseignant' ? 'Permanent' : 'Vacataire'}</span></td>
                                            <td><strong>${teacher.total_hours}h</strong></td>
                                            <td>${teacher.cours_hours}h</td>
                                            <td>${teacher.td_hours}h</td>
                                            <td>${teacher.tp_hours}h</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Modules Summary -->
                    <div class="mb-4">
                        <h5><i class="fas fa-book me-2"></i>Modules et Affectations</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Module</th>
                                        <th>Niveau</th>
                                        <th>Semestre</th>
                                        <th>Volume Horaire</th>
                                        <th>Enseignant Assigné</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${modules.map(module => `
                                        <tr>
                                            <td><strong>${module.nom}</strong></td>
                                            <td>${module.niveau || 'N/A'}</td>
                                            <td>${module.semestre || 'N/A'}</td>
                                            <td>${module.volume_horaire || 0}h</td>
                                            <td>${module.enseignant || 'Non assigné'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // Create workload distribution chart
        function createWorkloadChart() {
            const ctx = document.getElementById('workloadChart');
            if (!ctx) return;

            const workload = reportData.workload || [];
            const coursTotal = workload.reduce((sum, t) => sum + parseInt(t.cours_hours || 0), 0);
            const tdTotal = workload.reduce((sum, t) => sum + parseInt(t.td_hours || 0), 0);
            const tpTotal = workload.reduce((sum, t) => sum + parseInt(t.tp_hours || 0), 0);

            if (charts.workloadChart) {
                charts.workloadChart.destroy();
            }

            charts.workloadChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Cours', 'TD', 'TP'],
                    datasets: [{
                        data: [coursTotal, tdTotal, tpTotal],
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + 'h';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create modules chart
        function createModulesChart() {
            const ctx = document.getElementById('modulesChart');
            if (!ctx) return;

            const modules = reportData.modules || [];
            const semesterCounts = {};

            modules.forEach(module => {
                const semester = module.semestre || 'Non spécifié';
                semesterCounts[semester] = (semesterCounts[semester] || 0) + 1;
            });

            if (charts.modulesChart) {
                charts.modulesChart.destroy();
            }

            charts.modulesChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(semesterCounts),
                    datasets: [{
                        label: 'Nombre de Modules',
                        data: Object.values(semesterCounts),
                        backgroundColor: '#667eea',
                        borderColor: '#764ba2',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        // Export to PDF
        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Add title
            doc.setFontSize(20);
            doc.text('Rapport du Département', 20, 20);
            doc.setFontSize(14);
            doc.text('<?php echo htmlspecialchars($departmentName); ?>', 20, 30);
            doc.setFontSize(10);
            doc.text(`Année Académique: ${currentFilters.academicYear}`, 20, 40);
            doc.text(`Généré le: ${new Date().toLocaleDateString('fr-FR')}`, 20, 50);

            // Add statistics
            const stats = reportData.stats || {};
            doc.setFontSize(12);
            doc.text('Statistiques Générales:', 20, 70);
            doc.setFontSize(10);
            doc.text(`Nombre d'enseignants: ${stats.total_teachers || 0}`, 30, 80);
            doc.text(`Nombre de modules: ${reportData.modules?.length || 0}`, 30, 90);
            doc.text(`Heures totales: ${stats.total_hours || 0}h`, 30, 100);
            doc.text(`Moyenne par enseignant: ${Math.round(stats.average_hours || 0)}h`, 30, 110);

            // Add teachers table
            if (reportData.workload && reportData.workload.length > 0) {
                doc.setFontSize(12);
                doc.text('Charge de Travail des Enseignants:', 20, 130);

                let yPos = 140;
                doc.setFontSize(8);
                doc.text('Nom', 20, yPos);
                doc.text('Type', 70, yPos);
                doc.text('Total', 100, yPos);
                doc.text('Cours', 120, yPos);
                doc.text('TD', 140, yPos);
                doc.text('TP', 160, yPos);

                yPos += 10;
                reportData.workload.forEach(teacher => {
                    if (yPos > 270) {
                        doc.addPage();
                        yPos = 20;
                    }
                    doc.text(`${teacher.prenom} ${teacher.nom}`, 20, yPos);
                    doc.text(teacher.role === 'enseignant' ? 'Perm.' : 'Vac.', 70, yPos);
                    doc.text(`${teacher.total_hours}h`, 100, yPos);
                    doc.text(`${teacher.cours_hours}h`, 120, yPos);
                    doc.text(`${teacher.td_hours}h`, 140, yPos);
                    doc.text(`${teacher.tp_hours}h`, 160, yPos);
                    yPos += 8;
                });
            }

            // Save the PDF
            doc.save(`rapport_departement_${currentFilters.academicYear}.pdf`);
        }

        // Utility functions
        function showLoading() {
            document.getElementById('reportContent').innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Génération du rapport...</span>
                    </div>
                </div>
            `;
            document.getElementById('reportContent').style.display = 'block';
            document.getElementById('defaultMessage').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('reportContent').innerHTML = `
                <div class="report-content text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5 class="text-danger">Erreur</h5>
                    <p class="text-muted">${message}</p>
                </div>
            `;
            document.getElementById('reportContent').style.display = 'block';
            document.getElementById('defaultMessage').style.display = 'none';
        }
    </script>
</body>
</html>
