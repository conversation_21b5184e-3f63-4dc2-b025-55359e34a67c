<?php
// Vérifier l'authentification pour tous les rôles
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Récupérer les informations de l'utilisateur
$userRole = $_SESSION['user']['role'] ?? '';
// Essayer différentes clés pour l'ID utilisateur
$userId = $_SESSION['user']['id_user'] ?? $_SESSION['user']['id'] ?? $_SESSION['user']['user_id'] ?? null;
$userName = $_SESSION['user']['nom'] ?? $_SESSION['user']['username'] ?? $_SESSION['user']['prenom'] ?? 'Utilisateur';

// Vérifier que l'ID utilisateur est valide
if (!$userId || !is_numeric($userId)) {
    // Si aucun ID valide n'est trouvé, rediriger vers la connexion
    header('Location: ../../auth/login.php');
    exit();
}



// Configuration de la page
$pageTitle = "Messages";
$currentPage = "messages";

require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../model/messagesModel.php';
require_once __DIR__ . '/../../controller/messagesController.php';

// Get unread count
$unreadCount = getUnreadCount();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/messages.css">

    <style>
        .messages-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem 0;
            border-bottom: 2px solid #e9ecef;
        }

        .messages-header h1 {
            color: #2c3e50;
            font-weight: 600;
            margin: 0;
        }

        .messages-actions {
            display: flex;
            gap: 1rem;
        }

        .create-message-btn, .mark-all-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .create-message-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .create-message-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .mark-all-btn {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .mark-all-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .role-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-left: 1rem;
        }

        .role-admin { background: #dc3545; color: white; }
        .role-chef { background: #28a745; color: white; }
        .role-coordinateur { background: #17a2b8; color: white; }
        .role-enseignant { background: #ffc107; color: #212529; }
        .role-vacataire { background: #6f42c1; color: white; }
        .role-etudiant { background: #fd7e14; color: white; }

        /* Styles pour les messages avec couleurs pastel */
        .message-item {
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 1.25rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        .message-item:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            transform: translateY(-3px);
        }

        /* Messages reçus - Vert pastel */
        .message-item.message-received {
            background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
            border-left: 4px solid #a8d8a8;
        }

        .message-item.message-received.unread {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4f0d4 100%);
            border-left: 4px solid #7cc87c;
            box-shadow: 0 2px 8px rgba(124, 200, 124, 0.2);
        }

        /* Messages envoyés - Bleu pastel */
        .message-item.message-sent {
            background: linear-gradient(135deg, #f0f6ff 0%, #e6f2ff 100%);
            border-left: 4px solid #a8c8f0;
        }

        .message-item.message-sent.unread {
            background: linear-gradient(135deg, #e6f2ff 0%, #d1e7ff 100%);
            border-left: 4px solid #7db3f0;
            box-shadow: 0 2px 8px rgba(125, 179, 240, 0.2);
        }

        /* Messages généraux - Orange pastel */
        .message-item.message-general {
            background: linear-gradient(135deg, #fff8f0 0%, #ffefdc 100%);
            border-left: 4px solid #f0c674;
        }

        .message-item.message-general.unread {
            background: linear-gradient(135deg, #ffefdc 0%, #ffe4c4 100%);
            border-left: 4px solid #e6b85c;
            box-shadow: 0 2px 8px rgba(230, 184, 92, 0.2);
        }

        .message-icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        /* Icônes pour messages reçus */
        .message-received .message-icon {
            background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
            color: #2e7d32;
        }

        /* Icônes pour messages envoyés */
        .message-sent .message-icon {
            background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
            color: #1565c0;
        }

        /* Icônes pour messages généraux */
        .message-general .message-icon {
            background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
            color: #ef6c00;
        }

        /* Conteneur des messages */
        .notifications-list {
            width: 100%;
            max-width: 100%;
            overflow: hidden;
        }

        /* Styles pour les messages compacts */
        .message-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            min-width: 0; /* Permet la compression du contenu */
        }

        .message-title {
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            font-size: 1rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }

        .message-title.unread {
            font-weight: 700;
        }

        .message-meta {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: #6c757d;
            flex-wrap: wrap;
        }

        .message-date {
            font-size: 0.8rem;
            color: #8e8e93;
            white-space: nowrap;
        }

        /* Partie gauche du message (titre et meta) */
        .message-content-left {
            flex: 1;
            min-width: 0;
            margin-right: 1rem;
        }

        /* Partie droite du message (badges) */
        .message-content-right {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
        }

        .message-meta {
            font-size: 0.875rem;
        }

        .pagination-container {
            margin-top: 2rem;
        }

        .pagination .page-link {
            color: #667eea;
            border-color: #dee2e6;
        }

        .pagination .page-item.active .page-link {
            background-color: #667eea;
            border-color: #667eea;
        }

        .pagination .page-link:hover {
            color: #5a6fd8;
            background-color: #f8f9ff;
            border-color: #dee2e6;
        }

        /* Styles pour le modal de visualisation */
        .message-modal .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .message-modal .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }

        .message-modal .modal-title {
            font-weight: 600;
            font-size: 1.25rem;
        }

        .message-modal .btn-close {
            filter: brightness(0) invert(1);
        }

        .message-modal .modal-body {
            padding: 2rem;
            background: #fafbfc;
        }

        .message-content-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
        }

        .message-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .message-info-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .message-info-label {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .message-info-value {
            color: #2c3e50;
            font-weight: 500;
        }

        .message-content-text {
            line-height: 1.6;
            color: #2c3e50;
            font-size: 1rem;
        }

        .message-type-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .message-type-badge.received {
            background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
            color: #2e7d32;
        }

        .message-type-badge.sent {
            background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
            color: #1565c0;
        }

        .message-type-badge.general {
            background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
            color: #ef6c00;
        }

        /* Styles pour éviter le débordement horizontal */
        .container-fluid {
            overflow-x: hidden;
        }

        .main-content {
            overflow-x: hidden;
        }

        /* Responsive design pour les messages */
        @media (max-width: 768px) {
            .message-item {
                padding: 0.75rem 1rem;
            }

            .message-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .message-content-right {
                align-items: flex-start;
                flex-direction: row;
                gap: 0.5rem;
            }

            .message-title {
                white-space: normal;
                line-height: 1.3;
            }
        }

        @media (max-width: 576px) {
            .message-item {
                padding: 0.5rem 0.75rem;
            }

            .message-icon {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboards-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="messages-header">
                    <div>
                        <h1>
                            <i class="fas fa-envelope me-3"></i>
                            Messages
                        </h1>
                        <span class="role-badge role-<?php echo str_replace(' ', '-', strtolower($userRole)); ?>">
                            <?php echo ucfirst($userRole); ?>
                        </span>
                    </div>
                    <div class="messages-actions">
                        <?php if (in_array($userRole, ['admin', 'chef de departement'])): ?>
                        <button id="createMessageBtn" class="create-message-btn">
                            <i class="fas fa-plus me-1"></i>
                            Créer un message
                        </button>
                        <?php endif; ?>
                        <button id="markAllBtn" class="mark-all-btn">
                            <i class="fas fa-check-double me-1"></i>
                            Tout marquer comme lu
                        </button>
                    </div>
                </div>

                <div id="notificationsList" class="notifications-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2 text-muted">Chargement des messages...</p>
                    </div>
                </div>

                <!-- Pagination will be added here by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Message Creation Modal -->
    <?php if (in_array($userRole, ['admin', 'chef de departement'])): ?>
    <div class="modal fade" id="createMessageModal" tabindex="-1" aria-labelledby="createMessageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createMessageModalLabel">
                        <i class="fas fa-envelope me-2"></i>
                        Créer un nouveau message
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createMessageForm">
                        <div class="mb-3">
                            <label for="messageTitle" class="form-label">Titre du message</label>
                            <input type="text" class="form-control" id="messageTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="messageContent" class="form-label">Contenu</label>
                            <textarea class="form-control" id="messageContent" rows="5" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="messageReceiver" class="form-label">Destinataire (optionnel)</label>
                            <select class="form-select" id="messageReceiver">
                                <option value="">Message général (tous les utilisateurs)</option>
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="createMessage()">
                        <i class="fas fa-paper-plane me-1"></i>
                        Envoyer
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Modal de visualisation des messages -->
    <div class="modal fade message-modal" id="messageViewModal" tabindex="-1" aria-labelledby="messageViewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageViewModalLabel">
                        <i class="fas fa-envelope-open me-2"></i>
                        <span id="modalMessageTitle">Titre du message</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Informations du message -->
                    <div class="message-info-grid">
                        <div class="message-info-item">
                            <div class="message-info-label">Type de message</div>
                            <div class="message-info-value">
                                <span id="modalMessageType" class="message-type-badge">
                                    <i class="fas fa-envelope me-1"></i>
                                    <span>Type</span>
                                </span>
                            </div>
                        </div>
                        <div class="message-info-item">
                            <div class="message-info-label">Date d'envoi</div>
                            <div class="message-info-value" id="modalMessageDate">Date</div>
                        </div>
                        <div class="message-info-item">
                            <div class="message-info-label">Expéditeur</div>
                            <div class="message-info-value" id="modalMessageSender">Expéditeur</div>
                        </div>
                        <div class="message-info-item">
                            <div class="message-info-label">Destinataire</div>
                            <div class="message-info-value" id="modalMessageReceiver">Destinataire</div>
                        </div>
                    </div>

                    <!-- Contenu du message -->
                    <div class="message-content-section">
                        <div class="message-info-label mb-3">Contenu du message</div>
                        <div class="message-content-text" id="modalMessageContent">
                            Contenu du message...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Configuration pour le JavaScript - éviter les conflits de variables
        window.MessagesConfig = window.MessagesConfig || {};
        window.MessagesConfig.userRole = '<?php echo $userRole; ?>';
        window.MessagesConfig.userId = <?php echo $userId ? $userId : 'null'; ?>;
        window.MessagesConfig.basePath = '../../route/messagesRoute.php';

        // Variables globales pour la pagination
        let currentPage = 1;
        let totalPages = 1;

        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            loadMessages();

            // Event listeners pour les boutons
            const createBtn = document.getElementById('createMessageBtn');
            if (createBtn) {
                createBtn.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('createMessageModal'));
                    modal.show();
                });
            }

            const markAllBtn = document.getElementById('markAllBtn');
            if (markAllBtn) {
                markAllBtn.addEventListener('click', markAllAsRead);
            }
        });

        // Fonction pour charger les messages filtrés par utilisateur
        function loadMessages(page = 1) {
            if (!window.MessagesConfig.userId) {
                console.error('User ID not available');
                document.getElementById('notificationsList').innerHTML =
                    '<div class="alert alert-danger">Erreur: ID utilisateur non disponible.</div>';
                return;
            }

            currentPage = page;

            // Afficher le spinner de chargement
            document.getElementById('notificationsList').innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2 text-muted">Chargement des messages...</p>
                </div>
            `;

            fetch(`${window.MessagesConfig.basePath}?action=getByUser&user_id=${window.MessagesConfig.userId}&page=${page}&perPage=10`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    displayMessages(data.messages || []);
                    updatePagination(data.pages || 1, data.current_page || 1);
                    totalPages = data.pages || 1;
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                    document.getElementById('notificationsList').innerHTML =
                        '<div class="alert alert-danger">Erreur lors du chargement des messages: ' + error.message + '</div>';
                });
        }

        // Fonction pour créer un message
        function createMessage() {
            const title = document.getElementById('messageTitle').value.trim();
            const content = document.getElementById('messageContent').value.trim();
            const receiverId = document.getElementById('messageReceiver').value;

            if (!title || !content) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            const messageData = {
                title: title,
                content: content,
                sender_id: window.MessagesConfig.userId
            };

            if (receiverId) {
                messageData.receiver_id = receiverId;
            }

            fetch('../../route/messagesRoute.php?action=create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(messageData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Message envoyé avec succès !');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createMessageModal'));
                    modal.hide();
                    document.getElementById('createMessageForm').reset();
                    loadMessages(); // Recharger les messages
                } else {
                    alert('Erreur lors de l\'envoi du message.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de l\'envoi du message.');
            });
        }

        // Fonction pour afficher les messages avec le nouveau design
        function displayMessages(messages) {
            const container = document.getElementById('notificationsList');

            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun message trouvé</h5>
                        <p class="text-muted">Vous n'avez aucun message pour le moment.</p>
                    </div>
                `;
                return;
            }

            let html = '';
            messages.forEach(message => {
                const messageTypeClass = getMessageTypeClass(message.message_type);
                const messageTypeIcon = getMessageTypeIcon(message.message_type, message.is_read == 0);
                const isUnread = message.is_read == 0;

                html += `
                    <div class="message-item ${isUnread ? 'unread' : ''} ${messageTypeClass}"
                         data-message-id="${message.id}"
                         onclick="openMessageModal(${message.id})"
                         data-message='${JSON.stringify(message).replace(/'/g, "&#39;")}'>
                        <div class="d-flex align-items-center">
                            <div class="message-icon">
                                <i class="${messageTypeIcon}"></i>
                            </div>
                            <div class="message-header">
                                <div class="message-content-left">
                                    <h6 class="message-title ${isUnread ? 'unread' : ''}">${escapeHtml(message.title)}</h6>
                                    <div class="message-meta">
                                        <span class="message-date">
                                            <i class="fas fa-clock me-1"></i>
                                            ${formatDate(message.created_at)}
                                        </span>
                                        ${isUnread ? '<span class="badge bg-primary ms-2">Nouveau</span>' : ''}
                                    </div>
                                </div>
                                <div class="message-content-right">
                                    <span class="badge ${getMessageTypeBadgeClass(message.message_type)}">
                                        ${message.type_label}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Fonction pour marquer tous les messages comme lus (version utilisateur spécifique)
        function markAllAsRead() {
            if (!window.MessagesConfig.userId) {
                alert('Erreur: ID utilisateur non disponible.');
                return;
            }

            fetch('../../route/messagesRoute.php?action=markAllAsReadForUser', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: window.MessagesConfig.userId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadMessages(currentPage); // Recharger les messages
                    showNotification('Tous les messages ont été marqués comme lus.', 'success');
                } else {
                    showNotification('Erreur lors de la mise à jour.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Erreur lors de la mise à jour.', 'error');
            });
        }

        // Fonction pour marquer un message spécifique comme lu
        function markMessageAsRead(messageId) {
            fetch('../../route/messagesRoute.php?action=markAsRead', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: messageId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadMessages(currentPage); // Recharger les messages
                } else {
                    showNotification('Erreur lors de la mise à jour.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Erreur lors de la mise à jour.', 'error');
            });
        }

        // Fonctions utilitaires pour l'affichage des messages
        function getMessageTypeClass(messageType) {
            switch(messageType) {
                case 'received': return 'message-received';
                case 'sent': return 'message-sent';
                case 'general': return 'message-general';
                default: return '';
            }
        }

        function getMessageTypeIcon(messageType, isUnread = false) {
            switch(messageType) {
                case 'received':
                    return isUnread ? 'fas fa-envelope' : 'fas fa-envelope-open';
                case 'sent':
                    return isUnread ? 'fas fa-paper-plane' : 'fas fa-check-circle';
                case 'general':
                    return isUnread ? 'fas fa-bullhorn' : 'fas fa-volume-up';
                default:
                    return isUnread ? 'fas fa-envelope' : 'fas fa-envelope-open';
            }
        }

        function getMessageTypeBadgeClass(messageType) {
            switch(messageType) {
                case 'received': return 'bg-success';
                case 'sent': return 'bg-info';
                case 'general': return 'bg-warning text-dark';
                default: return 'bg-secondary';
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('fr-FR', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Fonction pour mettre à jour la pagination
        function updatePagination(totalPages, currentPage) {
            const container = document.getElementById('notificationsList').parentNode;

            // Supprimer l'ancienne pagination
            const existingPagination = container.querySelector('.pagination-container');
            if (existingPagination) {
                existingPagination.remove();
            }

            if (totalPages <= 1) {
                return; // Pas besoin de pagination
            }

            const paginationHtml = `
                <div class="pagination-container mt-4">
                    <nav aria-label="Navigation des messages">
                        <ul class="pagination justify-content-center">
                            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                                <a class="page-link" href="#" onclick="loadMessages(${currentPage - 1}); return false;">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            ${generatePageNumbers(totalPages, currentPage)}
                            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                                <a class="page-link" href="#" onclick="loadMessages(${currentPage + 1}); return false;">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', paginationHtml);
        }

        function generatePageNumbers(totalPages, currentPage) {
            let html = '';
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadMessages(${i}); return false;">${i}</a>
                    </li>
                `;
            }

            return html;
        }

        // Fonction pour ouvrir le modal de visualisation d'un message
        function openMessageModal(messageId) {
            // Trouver le message dans la liste
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (!messageElement) {
                console.error('Message not found');
                return;
            }

            try {
                const messageData = JSON.parse(messageElement.getAttribute('data-message'));

                // Remplir le modal avec les données du message
                document.getElementById('modalMessageTitle').textContent = messageData.title;
                document.getElementById('modalMessageContent').textContent = messageData.content;
                document.getElementById('modalMessageDate').textContent = formatDate(messageData.created_at);

                // Type de message avec badge et icône
                const typeElement = document.getElementById('modalMessageType');
                const typeClass = getModalTypeClass(messageData.message_type);
                const typeIcon = getMessageTypeIcon(messageData.message_type, false);

                typeElement.className = `message-type-badge ${typeClass}`;
                typeElement.innerHTML = `
                    <i class="${typeIcon} me-1"></i>
                    <span>${messageData.type_label}</span>
                `;

                // Expéditeur et destinataire
                document.getElementById('modalMessageSender').textContent =
                    messageData.sender_name || 'Système';
                document.getElementById('modalMessageReceiver').textContent =
                    messageData.receiver_name || 'Tous les utilisateurs';

                // Marquer le message comme lu automatiquement
                if (messageData.is_read == 0) {
                    markMessageAsRead(messageId);
                }

                // Ouvrir le modal
                const modal = new bootstrap.Modal(document.getElementById('messageViewModal'));
                modal.show();

            } catch (error) {
                console.error('Error parsing message data:', error);
                showNotification('Erreur lors de l\'ouverture du message.', 'error');
            }
        }

        // Fonction pour obtenir la classe CSS du type de message dans le modal
        function getModalTypeClass(messageType) {
            switch(messageType) {
                case 'received': return 'received';
                case 'sent': return 'sent';
                case 'general': return 'general';
                default: return 'received';
            }
        }

        // Fonction pour afficher les notifications
        function showNotification(message, type = 'info') {
            // Supprimer les notifications existantes
            const existingNotifications = document.querySelectorAll('.notification-toast');
            existingNotifications.forEach(notification => notification.remove());

            // Créer la nouvelle notification
            const notification = document.createElement('div');
            notification.className = `notification-toast alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                max-width: 500px;
            `;

            const icon = type === 'success' ? 'check-circle' :
                        type === 'error' ? 'exclamation-triangle' :
                        'info-circle';

            notification.innerHTML = `
                <i class="fas fa-${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            document.body.appendChild(notification);

            // Auto-suppression après 5 secondes
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
